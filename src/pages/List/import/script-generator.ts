/* eslint-disable no-param-reassign */
import { LoginStateAtom } from '@/model/login';
import {
  // LivePlayPull,
  liveBackgroundMusic,
  liveBkg,
  liveImage,
  liveSpeech,
  liveVideo,
  midasbuyEvent,
  virtualmanTXCZ,
  MaterialsAvatar,
  MaterialsBase,
} from '@/utils/play-component';
import { allViews, commonStyle } from '@/utils/play-view';
import { ScriptPlugin, SinglePlugin } from '@/utils/template/index';
import { getParam, uuid } from '@tencent/midas-util';
import { PagedooPlayConfig } from '@tencent/pagedoo-time-navigator/es/type';
import { getRecoil } from 'recoil-nexus';

import { CommonStyle } from '@tencent/pagedoo-library';
import { ParsedScriptData, ScriptSceneData } from './parse-excel';

import { PlayConfig, PlayService } from '@/type/pagedoo';
import { newPlayConfig } from '@/utils/template';
import { cloneDeep } from 'lodash';

// 从数字人配置获取音色配置的函数
const getVoiceConfigFromVirtualHuman = (
  virtualMan: NonNullable<ScriptSceneData['数字人']>
): {
  id: string;
  gender?: string;
  type?: string;
  pagedoo?: boolean;
  style?: string;
  speed?: number;
  locale: string;
} => {
  // 如果有数字人配置，优先使用数字人的音色配置
  return {
    id: virtualMan.音色,
    gender: virtualMan.性别,
    style: virtualMan.语气,
    // 数字人配置中的自研字段：TRUE 表示自研，FALSE 表示非自研
    pagedoo: virtualMan.自研 === 'TRUE',
    speed: virtualMan.语速 ? parseFloat(virtualMan.语速) : 1.0,
    locale: virtualMan.地域,
  };
};

export enum TXCZ_TEMPLATE_COMPONENTS {
  BKG = '背景',
  VIDEO_BKG = '视频背景',
  VIDEO = '视频',
  SPEECH = '话术',
  FOREGROUND = '前景',
  TRANSITION = '转场',
  PRE_TRANSITION = '转场预加载',
  BACKGROUND_MUSIC = '背景音乐',
  LUCKY_BAG = '福袋广告',
  PURCHASE = '购买广告',
}

// 视频节点偏移时间
const videoNodeOffsetTime = 0;

// 判断是否为全屏模式 - 新逻辑：通过视频定位判断
const isFullScreenMode = (scene: ScriptSceneData): boolean => {
  return scene.视频?.定位 === '全屏';
};

export const format = (conf: PlayConfig): PlayConfig => {
  for (const timeline of conf.timeline) {
    timeline.node = timeline.node.filter(Boolean);
  }
  conf.timeline = conf.timeline.filter((i) => i.node.length > 0);
  if (conf.__config) {
    conf.__config.scaleX = 80;
  }
  return conf;
};

// 设置meta信息
// - size
export const setMeta =
  ({ size }: { size: [number, number] }) =>
  (conf: PlayConfig) => {
    conf.meta = {
      ...(conf.meta ?? {}),
      size,
    };
  };

// 初始化时间轴
export const initPlugin = (
  sceneData: ScriptSceneData[],
  length = 5
  // extra?: number[]
): SinglePlugin => {
  return (conf: PlayConfig) => {
    // 背景图
    // 数字人后景
    // 桌子
    // 商品图
    // 数字人前景
    for (let i = 0; i < length; i++) {
      conf.timeline[i] = {
        __config: { height: 40 },
        node: new Array(sceneData.length).fill(null),
      };
    }
    // if (extra?.length) {
    //   extra.forEach((index) => {
    //     conf.timeline[index] = {
    //       __config: { height: 40 },
    //       node: new Array(sceneData.length).fill(null),
    //     };
    //   });
    // }
  };
};

export const duration = (
  language: string,
  defaultDuration = 10000
): SinglePlugin => {
  // 语言速度映射
  const languageSpeedMap: Record<string, number> = {
    ar: 233,
    de: 233,
    en: 233,
    es: 233,
    fr: 233,
    id: 233,
    it: 233,
    ja: 233,
    ko: 233,
    ms: 233,
    pt: 233,
    ru: 233,
    th: 233,
    tr: 233,
    zh: 88,
  };

  const languageSpeed = languageSpeedMap[language] || 233;

  return (conf: PlayConfig) => {
    // timeline最大节点数量
    const max = Math.max(...conf.timeline.map((i) => i.node.length));
    let offset = 0;
    for (let i = 0; i < max; i++) {
      const speechWords: string | undefined = conf.timeline
        .map((timeline) => timeline.node[i])
        .filter(Boolean)
        .find(
          (i) =>
            i.component.id.includes('LiveSpeech') &&
            i.component.data.speechData.text
        )?.component.data.speechData.text;
      // 计算“列”的时长
      const duration = Math.max(
        speechWords ? speechWords.length * languageSpeed : defaultDuration, // 有数字人的情况按口播文案估算时长
        1000 // 无数字人的情况按1000估算时长（即10秒）
      );
      // 修改timeline这一列上所有Node的duration和offset
      let nodeDuration = 0;
      for (const pagedooPlayTimeline of conf.timeline) {
        const node = pagedooPlayTimeline.node[i];
        if (!node) continue;
        nodeDuration =
          node.duration < 0
            ? duration + node.duration
            : node.duration > 0
            ? node.duration
            : duration;
        node.offset += offset;
        node.duration = nodeDuration;
      }

      // 打环节标
      conf.fragment.push({
        __config: { name: '' },
        id: uuid(),
        offset,
      });
      offset += duration;
    }
  };
};

// 计算节点时长
export const durationWithNumber =
  (options: { duration?: number }): SinglePlugin =>
  (conf) => {
    const max = Math.max(...conf.timeline.map((i) => i.node.length));
    let offset = 0;
    for (let i = 0; i < max; i++) {
      const duration = options?.duration || 20000;
      for (const pagedooPlayTimeline of conf.timeline) {
        const node = pagedooPlayTimeline.node[i];
        if (!node) continue;
        node.duration = duration;
        node.offset = offset;
      }
      conf.fragment.push({
        __config: { name: '' },
        id: uuid(),
        offset,
      });
      offset += duration;
    }
  };
// 数字人需要liveid，未设置liveid会导致播放出错
export const livePlugin = (conf: PlayConfig) => {
  const loginState = getRecoil(LoginStateAtom);
  const liveID = loginState?.openid || 'live';
  for (const timeline of conf.timeline) {
    for (const node of timeline.node) {
      if (!node) continue;
      if (node.component.id.includes('Virtualman')) {
        node.component.data.liveID = liveID;
      }
    }
  }
};

// 腾讯充值直播间相关
// 添加背景图
const scriptTXCZBkgPlugin = (
  sceneData: ScriptSceneData[],
  timelineKey: number,
  key: number
): SinglePlugin => {
  return (conf: PagedooPlayConfig) => {
    for (const scene of sceneData) {
      const index = sceneData.indexOf(scene);
      if (scene.背景) {
        conf.timeline[timelineKey].node[index] = {
          __config: { thumbnail: '', title: '', type: 'component' },
          actualDuration: 0,
          component: liveBkg(
            key,
            scene.背景 || '', // 背景图
            '', // prompt
            allViews['腾讯充值'].bkg
          ),
          duration: 0,
          id: uuid(),
          key,
          offset: 0,
        };
      }
    }
  };
};

// BGM
const scriptAdBackgroundMusicPlugin = (
  sceneData: ScriptSceneData[],
  timelineKey: number,
  key: number
): SinglePlugin => {
  return (conf: PlayConfig) => {
    for (const [index, scene] of sceneData.entries()) {
      if (!scene.音乐?.链接) continue;

      const fullScreen = scene.视频?.定位 === '全屏';
      const isPlayingGame = scene.场景?.名称 === '互动' && scene.视频;
      conf.timeline[timelineKey].node[index] = {
        __config: { thumbnail: '', title: '', type: 'component' },
        actualDuration: 0,
        component: liveBackgroundMusic(
          key,
          fullScreen || isPlayingGame ? 1 : 10,
          [
            {
              id: uuid(),
              title: 'springlive',
              url: scene.音乐?.链接,
              duration: '',
            },
          ],
          true,
          allViews['腾讯充值'].hidden
        ),
        duration: 0,
        id: uuid(),
        key,
        offset: 0,
      };
    }
  };
};

function getVideoDuration(url: string): Promise<number> {
  return new Promise((resolve, reject) => {
    const video = document.createElement('video');

    video.addEventListener('loadedmetadata', () => {
      const { duration } = video;
      resolve(duration * 1000);

      // Clean up
      video.remove();
    });

    video.addEventListener('error', (error) => {
      reject(error);

      // Clean up
      video.remove();
    });

    video.src = url;
    video.style.display = 'none';
    document.body.appendChild(video);
  });
}

// 添加视频
const scriptTXCZVideoPlugin = async (
  sceneData: ScriptSceneData[],
  timelineKey: number,
  key: number
): Promise<SinglePlugin> => {
  const videoPositions: Record<string, CommonStyle> = {
    全屏: commonStyle(0, 0, 455),
    顶部: commonStyle(0, 0, 455),
    靠上: commonStyle(0, 50, 455),
    居中: commonStyle(0, 295, 455),
    左上: commonStyle(22, 57, 190),
    右上: commonStyle(254, 60, 190),
  };
  return async (conf: PagedooPlayConfig) => {
    for (const scene of sceneData) {
      const index = sceneData.indexOf(scene);
      if (scene?.视频?.链接) {
        const _duration = await getVideoDuration(scene.视频.链接).catch(
          () => 0
        );
        conf.timeline[timelineKey].node[index] = {
          __config: { thumbnail: '', title: '', type: 'component' },
          actualDuration: 0,
          component: liveVideo(
            key,
            scene.视频.链接, // 视频URL
            videoPositions[scene.视频.定位 || '全屏'] || videoPositions.全屏,
            0,
            false
          ),
          duration: _duration - videoNodeOffsetTime,
          id: uuid(),
          key,
          offset: videoNodeOffsetTime,
        };
      }
    }
  };
};
// 主播打游戏
// const scriptTXCZPlayPullPlugin = (
//   sceneData: ScriptSceneData[],
//   timelineKey: number,
//   key: number
// ): SinglePlugin => {
//   return (conf: PagedooPlayConfig) => {
//     for (const scene of sceneData) {
//       const index = sceneData.indexOf(scene);
//       if (scene?.视频) {
//         if (!scene.视频.链接 && scene.视频.名称?.includes('live')) {
//           conf.timeline[timelineKey].node[index] = {
//             __config: { thumbnail: '', title: '', type: 'component' },
//             actualDuration: 0,
//             component: LivePlayPull(
//               key,
//               'https://csycdn.flv.wxqcloud.qq.com/trtc_1400419933/111583_136b1b36e9798f568501263dc4a775d2.flv?extbuf=uONV8K0iSzxVraFHuTqAo%2FeA938iEOnfe9zpH6bgwQb9bkQLwaVFMVQ5FMFgy%2FqBw3hAhYnzFq%2BEkWVoe1Q3vRwbR2QAIf0M5p3cFFVl3jtO8%2Bq%2F3LeSzKT%2B9veFC0YceyO1W7tYRNf4Bgbj78qb6dDCyRG3&openid=d80d9823b28181c3b7aeb539dac1a0ad&txSecret=975935d94f520656adacb4029e5beb7e&txTime=66A5BD54&wxtoken=0bdfb5eb623a2d8a7cd895ebba4a541c&cdntagname=nf430&combuf=EHH1PZ1ptGGm%2F2TelPfGAiqCvbmt0I7DKGjwLFYs51s1XCiY7LPKRNDuVnz1k7QB36%2FgJDGZCvwAkH6qWXx5IF9jsdGArUh96f%2BFVEv3YhSLBRD4NgaYZIH6lhKMU9A%2FfsIfgiNHplt0lMV%2B0mauKZRkM6mNTq6vtwyDg6zwgsfvTpjUIW5IMLcMKa8sKKssvJ94SZJULjTWT2thhRKeP1tTxIBBNs%2BD1XLvJXHfSG4%2FT6dY&sc=7&wu=1&tencent_test_client_ip=*************', // 视频URL
//               (allViews['腾讯充值'] as any)[
//                 scene.视频.定位 || 'phone_in_portrait_orientation'
//               ]
//             ),
//             duration: 0 - videoNodeOffsetTime,
//             id: uuid(),
//             key,
//             offset: videoNodeOffsetTime,
//           };
//         }
//       }
//     }
//   };
// };
const createScriptTXCZVirtualPlugin = (
  virtualManConf: Record<string, any>,
  getStyle: (scene: ScriptSceneData) => CommonStyle,
  getWrapperStyle?: (scene: ScriptSceneData) => React.CSSProperties
) => {
  return (
    sceneData: ScriptSceneData[],
    timelineKey: number,
    key: number
  ): SinglePlugin => {
    return (conf: PagedooPlayConfig) => {
      for (const scene of sceneData) {
        const index = sceneData.indexOf(scene);
        if (scene?.数字人) {
          const voiceConfig = getVoiceConfigFromVirtualHuman(scene.数字人);
          const virtualmanLabel = virtualManConf.label;
          const isMainVirtualman = scene.数字人.名称 === virtualmanLabel;
          const isInactive = !isMainVirtualman;
          const hidden = scene.视频?.定位 === '全屏' || isInactive;

          // 判断是否为互动模式
          const isInteractive = scene.场景?.名称 === '互动';

          const component = virtualmanTXCZ(
            key,
            getStyle(scene),
            virtualManConf,
            isInteractive && !isInactive ? 'comment' : 'text',
            isMainVirtualman,
            {
              platform: 'rvc',
              speed:
                getParam('tts_speed') ||
                scene.数字人.语速 ||
                voiceConfig.speed ||
                1,
              style:
                getParam('tts_style') ||
                scene.数字人.语气 ||
                voiceConfig?.style,
              voiceId:
                getParam('tts_id') || scene.数字人.音色 || voiceConfig.id,
              driverMode: 'voice',
              voiceExtendConfig: JSON.stringify({
                ShortName:
                  getParam('tts_id') || scene.数字人.音色 || voiceConfig.id,
                Gender: scene.数字人.性别 || voiceConfig.gender || 'Female',
                Locale: scene.数字人.语种 || voiceConfig.locale,
              }),
            },
            getWrapperStyle?.(scene) || {}
          );
          conf.timeline[timelineKey].node[index] = {
            __config: { thumbnail: '', title: '', type: 'component' },
            actualDuration: 0,
            component,
            hidden,
            duration: 0,
            id: uuid(),
            key,
            offset: 0,
          };
        }
      }
    };
  };
};

// 动态创建数字人插件的函数
const createDynamicVirtualManPlugin = (
  virtualManName: string,
  sceneData: ScriptSceneData[]
) => {
  // 从场景数据中找到该数字人的配置信息
  const virtualManScene = sceneData.find(
    (scene) => scene.数字人?.名称 === virtualManName
  );
  const virtualManData = virtualManScene?.数字人;

  if (!virtualManData) {
    // 如果没有找到数字人数据，抛出错误
    throw new Error(
      `找不到数字人配置: ${virtualManName}。请确保数字人子表中包含该数字人的配置信息。`
    );
  }

  // 使用数字人子表中的key和名称
  const virtualManConfig = {
    key: virtualManData.key,
    label: virtualManData.名称,
    config:
      virtualManData.自研 === 'TRUE'
        ? {
            key: virtualManData.key,
            type: 'pagedoo',
            enabled: true,
            img: '',
            label: '',
            desc: '',
            chromaKey: 0.5,
            timbre: {
              voice_id: 'MiXi2',
              emotion: 'default',
              voice_speed: 1,
              character_id: 'MiXi2',
            },
          }
        : null,
  };

  // 根据数字人名称选择对应的样式函数
  let styleFunction = (_scene: ScriptSceneData) =>
    allViews['腾讯充值'].human_primary;

  if (virtualManName.includes('竖屏') || virtualManName.includes('portrait')) {
    styleFunction = (_scene: ScriptSceneData) =>
      allViews['腾讯充值'].human_using_phone_in_portrait_orientation;
  } else if (
    virtualManName.includes('横屏') ||
    virtualManName.includes('landscape')
  ) {
    styleFunction = (_scene: ScriptSceneData) =>
      allViews['腾讯充值'].human_using_phone_in_landscape_orientation;
  }

  return createScriptTXCZVirtualPlugin(virtualManConfig, styleFunction);
};

// 前景
const scriptTXCZForegroundPlugin = (
  sceneData: ScriptSceneData[],
  timelineKey: number,
  key: number
): SinglePlugin => {
  return (conf: PagedooPlayConfig) => {
    for (const scene of sceneData) {
      const index = sceneData.indexOf(scene);
      if (scene.前景 && !isFullScreenMode(scene)) {
        conf.timeline[timelineKey].node[index] = {
          __config: { thumbnail: '', title: '', type: 'component' },
          actualDuration: 0,
          component: liveImage(
            key,
            scene.前景 || '',
            allViews['腾讯充值'].foreground
          ),
          duration: 0,
          id: uuid(),
          key,
          offset: 0,
        };
      }
    }
  };
};

// 转场
const transitionDuration = 3500; // 3000ms有点长，可能画面还没出来人就开始说话。2500ms比较合适
const scriptTXCZTransitionsPlugin = (
  sceneData: ScriptSceneData[],
  timelineKey: number,
  key: number
): SinglePlugin => {
  return (conf: PagedooPlayConfig) => {
    for (const scene of sceneData) {
      const index = sceneData.indexOf(scene);
      if (scene?.转场) {
        conf.timeline[timelineKey].node[index] = {
          __config: { thumbnail: '', title: '', type: 'component' },
          actualDuration: 0,
          component: liveImage(
            key,
            scene.转场 || '',
            allViews['腾讯充值'].transition
          ),
          duration: transitionDuration,
          id: uuid(),
          key,
          offset: 0,
        };
      }
    }
  };
};

// 转场预加载
const scriptTXCZTransitionsPreloadPlugin = (
  sceneData: ScriptSceneData[],
  timelineKey: number,
  key: number
): SinglePlugin => {
  return (conf: PagedooPlayConfig) => {
    for (const scene of sceneData) {
      const index = sceneData.indexOf(scene);
      if (scene?.转场) {
        conf.timeline[timelineKey].node[index - 1] = {
          __config: { thumbnail: '', title: '', type: 'component' },
          actualDuration: 0,
          component: liveImage(
            key,
            scene.转场 || '',
            allViews['腾讯充值'].transition
          ),
          duration: 0 - transitionDuration,
          hidden: true,
          id: uuid(),
          key, // timeline[7]和timeline[8]的key需保持一致，确保相邻node同一张转场图片只加载1次
          offset: transitionDuration,
        };
      }
    }
  };
};

// Midasbuy 福袋广告
const scriptMidasbuyLuckyBagPlugin = (
  sceneData: ScriptSceneData[],
  timelineKey: number,
  key: number
): SinglePlugin => {
  return (conf: PagedooPlayConfig) => {
    for (const scene of sceneData) {
      const index = sceneData.indexOf(scene);
      if (scene?.事件 && scene.事件.类型 === '开启福袋抽奖') {
        const affectiveSecond =
          parseInt(scene.事件.在线时长 || '900', 10) || 900;
        conf.timeline[timelineKey].node[index] = {
          __config: { thumbnail: '', title: '', type: 'component' },
          actualDuration: 0,
          component: midasbuyEvent(key, {
            ad_id: scene.事件.广告ID || '',
            affective_second: affectiveSecond,
            event_type: 'START_LUCKY_BAG_LOTTERY',
          }),
          duration: 0,
          id: uuid(),
          key,
          offset: 0,
        };
      }
    }
  };
};

// Midasbuy 贴片广告
const scriptMidasbuyPurchasePlugin = (
  sceneData: ScriptSceneData[],
  timelineKey: number,
  key: number
): SinglePlugin => {
  return (conf: PagedooPlayConfig) => {
    for (const scene of sceneData) {
      const index = sceneData.indexOf(scene);
      if (scene?.事件 && scene.事件.类型 === '开启贴片广告') {
        const affectiveSecond =
          parseInt(scene.事件.在线时长 || '900', 10) || 900;
        conf.timeline[timelineKey].node[index] = {
          __config: { thumbnail: '', title: '', type: 'component' },
          actualDuration: 0,
          component: midasbuyEvent(
            key,
            {
              ad_id: scene.事件.广告ID || '',
              affective_second: affectiveSecond,
              event_type: 'PUSH_LIVE_POP_AD_START',
            },
            {
              ad_id: scene.事件.广告ID || '',
              event_type: 'PUSH_LIVE_POP_AD_STOP',
            }
          ),
          duration: 0,
          id: uuid(),
          key,
          offset: 0,
        };
      }
    }
  };
};

// 话术
const scriptTXCZSpeechPlugin = (
  sceneData: ScriptSceneData[],
  timelineKey: number,
  key: number
): SinglePlugin => {
  return (conf: PagedooPlayConfig) => {
    for (const scene of sceneData) {
      const index = sceneData.indexOf(scene);
      if (!isFullScreenMode(scene)) {
        const wordText = scene?.台词 || '';
        // 判断是否为互动模式或直播模式
        const isInteractive =
          scene.台词?.includes('互动') || scene.台词?.includes('comment');
        const isLiveVideo = scene.视频?.名称?.includes('live');

        conf.timeline[timelineKey].node[index] = {
          __config: { thumbnail: '', title: '', type: 'component' },
          actualDuration: 0,
          component: liveSpeech(
            key,
            {
              type: isInteractive || isLiveVideo ? 'answer' : 'text',
              text: wordText,
            },
            { type: 'virtualman' },
            allViews['腾讯充值'].hidden,
            isLiveVideo
          ),
          duration: 0,
          id: uuid(),
          key,
          offset: 0,
        };
      }
    }
  };
};

const createScriptTXCZduration =
  (language: string): SinglePlugin =>
  (conf) => {
    // 语言速度映射
    const languageSpeedMap: Record<string, number> = {
      ar: 233,
      de: 233,
      en: 233,
      es: 233,
      fr: 233,
      id: 233,
      it: 233,
      ja: 233,
      ko: 233,
      ms: 233,
      pt: 233,
      ru: 233,
      th: 233,
      tr: 233,
      zh: 88,
    };

    const languageSpeed = languageSpeedMap[language] || 233;

    const max = Math.max(...conf.timeline.map((i) => i.node.length));
    let offset = 0;
    for (let i = 0; i < max; i++) {
      const LiveSpeech = conf.timeline
        .map((timeline) => timeline.node[i])
        .filter(Boolean)
        .find(
          (i) =>
            i.component.id.includes('LiveSpeech') &&
            i.component.data.speechData.text // 注意不能漏了这里，否则会导致图片预加载逻辑出错
        );
      const LiveVideo = conf.timeline
        .map((timeline) => timeline.node[i])
        .filter(Boolean)
        .find((i) => i.component.id.includes('LiveVideo'));

      // 如果视频明确声明了duration，以视频的duration为准
      const _duration =
        LiveVideo?.duration && LiveVideo.duration > 0
          ? LiveVideo.duration
          : Math.max(
              LiveSpeech
                ? LiveSpeech.component.data.speechData.text.length *
                    languageSpeed
                : 10000,
              1000
            );
      for (const pagedooPlayTimeline of conf.timeline) {
        const node = pagedooPlayTimeline.node[i];
        if (!node) continue;
        node.duration =
          node.duration < 0
            ? _duration + node.duration
            : node.duration > 0
            ? node.duration
            : _duration;
        node.offset += offset;
      }
      conf.fragment.push({
        __config: { name: '' },
        id: uuid(),
        offset,
      });
      offset += _duration;
    }
    // const BackgroundMusic = conf.timeline
    //   .flatMap((timeline) => timeline.node)
    //   .filter(Boolean)
    //   .filter((data) => data.component.id.includes('BackgroundMusic'))[0];
    // if (BackgroundMusic) {
    //   BackgroundMusic.duration = offset;
    // }
  };

export function getTimelineIndex(init = -1) {
  const result = init + 1;
  return result;
}

export function getTimelineNodeKey(key = 2000) {
  return key + 1;
}

export const pagedooScriptPlugin = async (
  parsedScriptData: ParsedScriptData,
  language = 'zh',
  _options: {
    templateId?: string;
    isVideo?: boolean;
    isLive?: boolean;
  } = {
    templateId: undefined,
    isLive: undefined,
    isVideo: undefined,
  }
): Promise<ScriptPlugin> => {
  // 获取指定语言的分镜数据，如果没有则使用第一个可用的语言
  const availableLanguages = Object.keys(parsedScriptData.分镜数据);
  const targetLanguage = availableLanguages.includes(language)
    ? language
    : getParam('lan') || getParam('lang') || 'en';

  if (!targetLanguage) {
    throw new Error('没有找到可用的分镜数据');
  }

  const sceneData = parsedScriptData.分镜数据[targetLanguage];

  // 分析分镜数据中使用的数字人
  const usedVirtualHumans = new Set<string>();
  sceneData.forEach((scene) => {
    if (scene.数字人?.名称) {
      usedVirtualHumans.add(scene.数字人.名称);
    }
  });

  // 动态生成timeline，包含实际使用的数字人轨道数量
  const timeline = [
    TXCZ_TEMPLATE_COMPONENTS.BKG,
    TXCZ_TEMPLATE_COMPONENTS.VIDEO_BKG,
    TXCZ_TEMPLATE_COMPONENTS.VIDEO,
    // 动态添加数字人轨道
    ...Array.from(usedVirtualHumans).map((_, index) => `VIRTUALMAN_${index}`),
    TXCZ_TEMPLATE_COMPONENTS.SPEECH,
    TXCZ_TEMPLATE_COMPONENTS.FOREGROUND,
    TXCZ_TEMPLATE_COMPONENTS.TRANSITION,
    TXCZ_TEMPLATE_COMPONENTS.PRE_TRANSITION,
    TXCZ_TEMPLATE_COMPONENTS.BACKGROUND_MUSIC,
    TXCZ_TEMPLATE_COMPONENTS.LUCKY_BAG,
    TXCZ_TEMPLATE_COMPONENTS.PURCHASE,
  ];

  return [
    initPlugin(sceneData, timeline.length),
    // 背景
    scriptTXCZBkgPlugin(
      sceneData,
      timeline.indexOf(TXCZ_TEMPLATE_COMPONENTS.BKG),
      getTimelineNodeKey(timeline.indexOf(TXCZ_TEMPLATE_COMPONENTS.BKG))
    ),
    // 视频背景
    // scriptTXCZVideoBkgPlugin(
    //   sceneData,
    //   timeline.indexOf(TXCZ_TEMPLATE_COMPONENTS.VIDEO_BKG),
    //   getTimelineNodeKey(timeline.indexOf(TXCZ_TEMPLATE_COMPONENTS.VIDEO_BKG))
    // ),
    // 视频组件
    await scriptTXCZVideoPlugin(
      sceneData,
      timeline.indexOf(TXCZ_TEMPLATE_COMPONENTS.VIDEO),
      getTimelineNodeKey(timeline.indexOf(TXCZ_TEMPLATE_COMPONENTS.VIDEO))
    ),
    // 直播推流组件
    // scriptTXCZPlayPullPlugin(
    //   sceneData,
    //   timeline.indexOf(TXCZ_TEMPLATE_COMPONENTS.VIDEO),
    //   getTimelineNodeKey(timeline.indexOf(TXCZ_TEMPLATE_COMPONENTS.VIDEO))
    // ),
    // 动态生成数字人插件
    ...Array.from(usedVirtualHumans).map((virtualManName, index) => {
      const virtualManPlugin = createDynamicVirtualManPlugin(
        virtualManName,
        sceneData
      );
      const timelineKey = `VIRTUALMAN_${index}`;
      return virtualManPlugin(
        sceneData,
        timeline.indexOf(timelineKey),
        getTimelineNodeKey(timeline.indexOf(timelineKey))
      );
    }),
    // 话术组件（自研tts需要搭配话术组件使用）
    scriptTXCZSpeechPlugin(
      sceneData,
      timeline.indexOf(TXCZ_TEMPLATE_COMPONENTS.SPEECH),
      getTimelineNodeKey(timeline.indexOf(TXCZ_TEMPLATE_COMPONENTS.SPEECH))
    ),
    // 前景
    scriptTXCZForegroundPlugin(
      sceneData,
      timeline.indexOf(TXCZ_TEMPLATE_COMPONENTS.FOREGROUND),
      getTimelineNodeKey(timeline.indexOf(TXCZ_TEMPLATE_COMPONENTS.FOREGROUND))
    ),
    // 转场
    scriptTXCZTransitionsPlugin(
      sceneData,
      timeline.indexOf(TXCZ_TEMPLATE_COMPONENTS.TRANSITION),
      getTimelineNodeKey(timeline.indexOf(TXCZ_TEMPLATE_COMPONENTS.TRANSITION))
    ),
    // 转场预加载
    scriptTXCZTransitionsPreloadPlugin(
      sceneData,
      timeline.indexOf(TXCZ_TEMPLATE_COMPONENTS.PRE_TRANSITION),
      getTimelineNodeKey(timeline.indexOf(TXCZ_TEMPLATE_COMPONENTS.TRANSITION)) // 与“转场”保持key值相同，保证切换环节时不会被销毁
    ),
    scriptAdBackgroundMusicPlugin(
      sceneData,
      timeline.indexOf(TXCZ_TEMPLATE_COMPONENTS.BACKGROUND_MUSIC),
      getTimelineNodeKey(
        timeline.indexOf(TXCZ_TEMPLATE_COMPONENTS.BACKGROUND_MUSIC)
      )
    ),
    scriptMidasbuyLuckyBagPlugin(
      sceneData,
      timeline.indexOf(TXCZ_TEMPLATE_COMPONENTS.LUCKY_BAG),
      getTimelineNodeKey(timeline.indexOf(TXCZ_TEMPLATE_COMPONENTS.LUCKY_BAG))
    ),
    scriptMidasbuyPurchasePlugin(
      sceneData,
      timeline.indexOf(TXCZ_TEMPLATE_COMPONENTS.PURCHASE),
      getTimelineNodeKey(timeline.indexOf(TXCZ_TEMPLATE_COMPONENTS.PURCHASE))
    ),
    createScriptTXCZduration(targetLanguage),
    livePlugin,
    format,
    setMeta({ size: [455, 812] }),
  ];
};

export const genPagedooScriptPlugin = async ({
  parsedScriptData,
  language = 'zh',
  templateId,
  isVideo = false,
  isLive = false,
}: {
  parsedScriptData: ParsedScriptData;
  language?: string;
  templateId: string;
  isVideo?: boolean;
  isLive?: boolean;
}): Promise<ScriptPlugin> => {
  return await pagedooScriptPlugin(parsedScriptData, language, {
    templateId,
    isLive,
    isVideo,
  });
};

export const playService = {
  async getPlayScript(script: ParsedScriptData) {
    return await newPlayConfig({
      plugins: [await pagedooScriptPlugin(script)],
    });
  },
  async getPageSchema(script: ParsedScriptData) {
    const playScript = await this.getPlayScript(script);
    return this.getPageSchemaFromConfig(playScript);
  },
  async getPageSchemaFromConfig(playScript: PlayConfig) {
    return {
      global: {
        pages: [
          {
            data: {
              components: [
                {
                  id: `component/${MaterialsBase}/Background`,
                  key: 0,
                  name: 'Background',
                  style: {},
                  commonStyle: {},
                  wrapperStyle: {},
                  data: {
                    __component_name: '背景设置',
                    backgroundConf: {
                      type: 'color',
                      backgroundColor: {
                        color: '',
                        show: true,
                        realColor: 'unset',
                      },
                    },
                  },
                  actions: [],
                  children: [
                    {
                      id: `component/${MaterialsAvatar}/Director`,
                      name: 'Director',
                      key: 1,
                      style: {},
                      commonStyle: {},
                      wrapperStyle: {},
                      chosen: false,
                      data: {
                        _v: 0,
                        play: true,
                        __component_name: '导演(Director)',
                        __component_sub_name: '导演组件 负责脚本执行，渲染画面',
                        __component_id: 'Director',
                        __component_mutex_data: '',
                        __pagedoo_i18n: {},
                      },
                      actions: [],
                    },
                  ],
                },
              ],
              plugins: [],
              version: {
                versionId: 'v606055e',
                versionName: 'default',
              },
            },
            page_id: 'index',
          },
        ],
        'pagedoo-play-script': playScript,
      },
    };
  },
  async getPageSchemaShot(script: ParsedScriptData) {
    const playScript = await this.getPlayScript(script);
    return this.getPageSchemaShotFromConfig(playScript);
  },
  async getPageSchemaShotFromConfig(playScript: PlayConfig) {
    const schema = await playService.getPageSchemaFromConfig(
      cloneDeep(playScript)
    );
    for (const pagedooPlayTimeline of schema.global['pagedoo-play-script']
      .timeline) {
      if (!pagedooPlayTimeline.node[0]) {
        pagedooPlayTimeline.node = [];
        continue;
      }
      pagedooPlayTimeline.node = [pagedooPlayTimeline.node[0]];
      // 虚拟人替换为假人
      if (pagedooPlayTimeline.node[0]?.component.id.includes('Virtualman')) {
        pagedooPlayTimeline.node[0].component = liveImage(
          pagedooPlayTimeline.node[0].key,
          'https://pagedoo.pay.qq.com/material/@platform/6b1a359f29a07ab29a569037f4c5988a.png',
          pagedooPlayTimeline.node[0].component.commonStyle
        );
      }
    }
    if (schema.global['pagedoo-play-script'].fragment.length)
      schema.global['pagedoo-play-script'].fragment.length = 1;
    return schema;
  },
};
