import * as XLSX from 'xlsx';
import { LANGUAGE_MAPPING } from './language';
import { playService } from './script-generator';

// TODO:当剧本包含某些语言，但是图片/音视频素材不包含对应语言的列时，给出报错

class ParseError extends Error {
  constructor(message: string, public reason?: unknown) {
    super(`解析错误: ${message}`);
    this.name = 'ParseError';
  }
}

// 校验错误类
class ValidationError extends Error {
  constructor(
    public tableName: string,
    public rowIndex: number,
    public fieldName: string,
    public value: unknown,
    public rule: string
  ) {
    super(
      `校验错误: 表格"${tableName}"第${
        rowIndex + 1
      }行字段"${fieldName}"的值"${value}"不符合规则: ${rule}`
    );
    this.name = 'ValidationError';
  }
}

// 字段校验规则类型
export type FieldValidationRule = {
  type:
    | 'required'
    | 'number'
    | 'positiveNumber'
    | 'boolean'
    | 'enum'
    | 'url'
    | 'custom';
  message?: string;
  options?: {
    min?: number;
    max?: number;
    enumValues?: readonly string[];
    customValidator?: (value: unknown) => boolean;
  };
};

// 表格校验规则配置
export type TableValidationRules = {
  [fieldName: string]: FieldValidationRule;
};

// 校验结果
export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

// 校验函数
export function validateField(
  value: string | number | undefined | null,
  rule: FieldValidationRule,
  tableName: string,
  rowIndex: number,
  fieldName: string
): ValidationError | null {
  const stringValue = value ? String(value).trim() : '';

  switch (rule.type) {
    case 'required':
      if (!stringValue) {
        return new ValidationError(
          tableName,
          rowIndex,
          fieldName,
          value,
          rule.message || '此字段为必填项'
        );
      }
      break;

    case 'number':
      if (stringValue && isNaN(Number(stringValue))) {
        return new ValidationError(
          tableName,
          rowIndex,
          fieldName,
          value,
          rule.message || '必须为数字'
        );
      }
      break;

    case 'positiveNumber': {
      const num = Number(stringValue);
      if (stringValue && (isNaN(num) || num <= 0)) {
        return new ValidationError(
          tableName,
          rowIndex,
          fieldName,
          value,
          rule.message || '必须为大于0的数字'
        );
      }
      break;
    }

    case 'boolean':
      if (
        stringValue &&
        !['TRUE', 'FALSE'].includes(stringValue.toUpperCase())
      ) {
        return new ValidationError(
          tableName,
          rowIndex,
          fieldName,
          value,
          rule.message || '必须为TRUE或FALSE'
        );
      }
      break;

    case 'enum':
      if (
        stringValue &&
        rule.options?.enumValues &&
        !rule.options.enumValues.includes(stringValue)
      ) {
        return new ValidationError(
          tableName,
          rowIndex,
          fieldName,
          value,
          rule.message ||
            `必须为以下值之一: ${rule.options.enumValues.join(', ')}`
        );
      }
      break;

    case 'url':
      if (stringValue && !isValidUrl(stringValue)) {
        return new ValidationError(
          tableName,
          rowIndex,
          fieldName,
          value,
          rule.message || '必须为有效的URL'
        );
      }
      break;

    case 'custom':
      if (
        stringValue &&
        rule.options?.customValidator &&
        !rule.options.customValidator(stringValue)
      ) {
        return new ValidationError(
          tableName,
          rowIndex,
          fieldName,
          value,
          rule.message || '不符合自定义校验规则'
        );
      }
      break;
  }

  return null;
}

// 辅助函数：检查是否为有效URL
function isValidUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

// 各个子表的校验规则配置
export const VALIDATION_RULES = {
  事件配置: {
    名称: { type: 'required' as const, message: '事件名称为必填项' },
    类型: {
      type: 'enum' as const,
      options: {
        enumValues: ['开启贴片广告', '关闭贴片广告', '开启福袋抽奖'],
      },
      message: '事件类型必须为: 开启贴片广告、关闭贴片广告、开启福袋抽奖之一',
    },
    在线时长: {
      type: 'positiveNumber' as const,
      message: '在线时长必须为大于0的数字',
    },
  },
  数字人配置: {
    名称: { type: 'required' as const, message: '数字人名称为必填项' },
    语种: { type: 'required' as const, message: '语种为必填项' },
    自研: {
      type: 'boolean' as const,
      message: '自研字段必须为TRUE或FALSE',
    },
    key: { type: 'required' as const, message: 'key为必填项' },
    性别: { type: 'required' as const, message: '性别为必填项' },
    地域: { type: 'required' as const, message: '地域为必填项' },
  },
  音视频素材: {
    名称: { type: 'required' as const, message: '音视频名称为必填项' },
    链接: { type: 'url' as const, message: '链接必须为有效的URL' },
    定位: {
      type: 'enum' as const,
      options: {
        enumValues: ['全屏', '顶部', '靠上', '居中', '左上', '右上'],
      },
      message: '定位必须为: 全屏、顶部、靠上、居中、左上、右上之一',
    },
    音量: {
      type: 'custom' as const,
      options: {
        customValidator: (value: unknown) => {
          if (!value) return true; // 音量是可选字段
          const str = String(value).trim();
          if (!str) return true;
          const num = Number(str);
          return !isNaN(num) && num >= 0 && num <= 100;
        },
      },
      message: '音量必须为0-100之间的数字',
    },
  },
  图片素材: {
    名称: { type: 'required' as const, message: '图片名称为必填项' },
    链接: { type: 'url' as const, message: '链接必须为有效的URL' },
  },
  场景配置: {
    名称: { type: 'required' as const, message: '场景名称为必填项' },
  },
} as const;

// 导出的校验函数：手动校验表格数据
export function validateTableData(
  data: Record<string, unknown>[],
  validationRules: TableValidationRules,
  tableName: string
): ValidationResult {
  const errors: ValidationError[] = [];

  data.forEach((row, rowIndex) => {
    Object.entries(validationRules).forEach(([fieldName, rule]) => {
      const fieldValue = row[fieldName];
      const error = validateField(
        fieldValue as string | number | undefined | null,
        rule,
        tableName,
        rowIndex,
        fieldName
      );
      if (error) {
        errors.push(error);
      }
    });
  });

  return {
    isValid: errors.length === 0,
    errors,
  };
}

// 导出的校验函数：校验所有子表数据
export function validateAllSubTables(data: ParsedScriptData): ValidationResult {
  const allErrors: ValidationError[] = [];

  // 校验事件配置
  const eventValidation = validateTableData(
    data.事件配置 as unknown as Record<string, unknown>[],
    VALIDATION_RULES.事件配置,
    '事件配置'
  );
  allErrors.push(...eventValidation.errors);

  // 校验数字人配置
  const virtualHumanValidation = validateTableData(
    data.数字人配置 as unknown as Record<string, unknown>[],
    VALIDATION_RULES.数字人配置,
    '数字人配置'
  );
  allErrors.push(...virtualHumanValidation.errors);

  // 校验音视频素材
  const videoValidation = validateTableData(
    data.音视频素材 as unknown as Record<string, unknown>[],
    VALIDATION_RULES.音视频素材,
    '音视频素材'
  );
  allErrors.push(...videoValidation.errors);

  // 校验图片素材
  const imageValidation = validateTableData(
    data.图片素材 as unknown as Record<string, unknown>[],
    VALIDATION_RULES.图片素材,
    '图片素材'
  );
  allErrors.push(...imageValidation.errors);

  // 校验场景配置
  const sceneValidation = validateTableData(
    data.场景配置 as unknown as Record<string, unknown>[],
    VALIDATION_RULES.场景配置,
    '场景配置'
  );
  allErrors.push(...sceneValidation.errors);

  return {
    isValid: allErrors.length === 0,
    errors: allErrors,
  };
}

// 辅助函数：从工作表名称中提取语言代码
export function extractLanguageCode(sheetName: string): string | null {
  // 直接匹配映射表中的语言名称
  for (const [chineseName, isoCode] of Object.entries(LANGUAGE_MAPPING)) {
    if (sheetName.includes(chineseName)) {
      return isoCode;
    }
  }

  // 如果没有找到匹配，检查是否已经是ISO代码格式
  const isoPattern = /^[a-z]{2}(-[A-Z]{2})?$/;
  if (isoPattern.test(sheetName)) {
    return sheetName;
  }

  return null;
}

// 分镜数据结构（原始）
export interface RawScriptSceneData {
  环节: string;
  台词: string;
  场景: string;
  数字人: string;
  背景: string;
  前景: string;
  视频: string;
  音乐: string;
  转场: string;
  事件: string;
}

// 分镜数据结构（链接后）
export interface ScriptSceneData {
  环节: string;
  台词: string;
  场景: SceneData | null;
  数字人: VirtualHumanData | null;
  视频: VideoAssetData | null;
  音乐: VideoAssetData | null; // 音频也在音视频表中
  事件: EventData | null;
  背景: string; // 图片URL或引用
  前景: string; // 图片URL或引用
  转场: string; // 图片URL或引用
}

// 图片素材数据结构
export interface ImageAssetData {
  名称: string;
  链接: string; // 合并后的URL
}

// 音视频素材数据结构
export interface VideoAssetData {
  名称: string;
  链接: string; // 合并后的URL
  定位?: '全屏' | '顶部' | '靠上' | '居中' | '左上' | '右上';
  音量?: string;
}

// 事件数据结构
export interface EventData {
  名称: string;
  类型: '开启贴片广告' | '关闭贴片广告' | '开启福袋抽奖';
  广告ID?: string;
  在线时长?: string;
  重名检测?: string;
}

// 数字人数据结构
export interface VirtualHumanData {
  名称: string;
  语种: string;
  语速: string;
  音色: string;
  语气?: string;
  自研: 'TRUE' | 'FALSE';
  key: string;
  性别: string;
  地域: string;
}

// 场景数据结构
export interface SceneData {
  名称: string;
  参数1?: string;
  参数2?: string;
  参数3?: string;
  参数4?: string;
}

// 完整的解析结果数据结构
export interface ParsedScriptData {
  教程?: string;
  分镜数据: {
    [language: string]: ScriptSceneData[];
  };
  图片素材: ImageAssetData[];
  音视频素材: VideoAssetData[];
  事件配置: EventData[];
  数字人配置: VirtualHumanData[];
  场景配置: SceneData[];
  检测到的语言: string[]; // 新增：记录所有检测到的语言代码
}

// 临时存储原始数据的扩展接口
interface ParsedScriptDataWithRaw extends ParsedScriptData {
  [key: string]: unknown;
}

// 表格解析选项
interface TableParseOptions {
  skipColumns?: string[]; // 要跳过的列名
  mergeLanguageUrls?: boolean; // 是否合并语言URL为单一链接
  detectedLanguages?: Set<string>; // 用于收集检测到的语言
  requiredField?: string; // 必须存在的字段名（如"名称"）
  validationRules?: TableValidationRules; // 校验规则
  tableName?: string; // 表格名称，用于错误报告
}

// 通用表格解析函数：将Excel表格数据转换为JSON Array of Objects
// 示例用法：
// 1. 简单表格：parseGenericTable<EventData>(jsonData)
//    输出：[{名称:"开启贴片_600UC",类型:"开启贴片广告",广告ID:"527_live_600uc",在线时长:"300"}]
// 2. 合并语言URL：parseGenericTable<ImageAssetData>(jsonData, {mergeLanguageUrls: true, requiredField: '名称'})
//    输出：[{名称:"背景图.png",链接:"https://example.com/image.png"}]
function parseGenericTable<T>(
  jsonData: (string | number)[][],
  options: TableParseOptions = {},
  startRow = 1
): T[] {
  if (jsonData.length <= startRow) return [];

  const {
    skipColumns = ['重名检测'],
    mergeLanguageUrls = false,
    detectedLanguages,
    requiredField,
    validationRules,
    tableName = '未知表格',
  } = options;

  const headers = jsonData[0] as string[];
  const result: T[] = [];
  const validationErrors: ValidationError[] = [];

  for (let i = startRow; i < jsonData.length; i++) {
    const row = jsonData[i];
    if (!row || row.length === 0) continue;

    const rowData: Record<string, string> = {};
    const languageUrls: Record<string, string> = {};

    headers.forEach((header, index) => {
      if (!header?.trim()) return;

      const cellValue = row[index];
      const trimmedHeader = header.trim();

      // 跳过指定的列
      if (skipColumns.some((skipCol) => trimmedHeader.includes(skipCol))) {
        return;
      }

      if (mergeLanguageUrls) {
        // 检查是否是语言列
        const languageCode = extractLanguageCode(trimmedHeader);

        if (languageCode && languageCode !== trimmedHeader) {
          // 这是一个语言URL列
          const url = cellValue ? String(cellValue).trim() : '';
          if (url) {
            languageUrls[languageCode] = url;
            if (detectedLanguages) {
              detectedLanguages.add(languageCode);
            }
          }
        } else {
          // 这是普通字段
          rowData[trimmedHeader] = cellValue ? String(cellValue).trim() : '';
        }
      } else {
        // 不合并语言URL，直接处理所有字段
        rowData[trimmedHeader] = cellValue ? String(cellValue).trim() : '';
      }
    });

    // 如果需要合并语言URL，选择第一个可用的URL作为链接
    if (mergeLanguageUrls) {
      const availableUrls = Object.values(languageUrls).filter((url) => url);
      if (availableUrls.length > 0) {
        const [firstUrl] = availableUrls;
        rowData['链接'] = firstUrl;
      }
    }

    // 检查是否满足必需字段要求
    if (requiredField && !rowData[requiredField]) {
      continue;
    }

    // 只添加有效的行（至少有一个非空字段）
    const hasValidData = Object.values(rowData).some(
      (value) => value && value.trim() !== ''
    );

    if (hasValidData) {
      // 执行字段校验
      if (validationRules) {
        Object.entries(validationRules).forEach(([fieldName, rule]) => {
          const fieldValue = rowData[fieldName];
          const error = validateField(
            fieldValue,
            rule,
            tableName,
            i,
            fieldName
          );
          if (error) {
            validationErrors.push(error);
          }
        });
      }

      result.push(rowData as T);
    }
  }

  // 如果有校验错误，抛出第一个错误
  if (validationErrors.length > 0) {
    throw validationErrors[0];
  }

  return result;
}

// 兼容性函数：保持原有的简单表格解析接口
function parseTableData<T>(jsonData: (string | number)[][], startRow = 1): T[] {
  return parseGenericTable<T>(jsonData, {}, startRow);
}

// 辅助函数：创建名称到数据的映射表
function createNameMap<T extends { 名称: string }>(items: T[]): Map<string, T> {
  const map = new Map<string, T>();
  items.forEach((item) => {
    if (item.名称?.trim()) {
      map.set(item.名称.trim(), item);
    }
  });
  return map;
}

// 辅助函数：创建数字人映射表，考虑语种和名称的组合
function createVirtualHumanMap(
  virtualHumans: VirtualHumanData[],
  targetLanguage?: string
): Map<string, VirtualHumanData> {
  const map = new Map<string, VirtualHumanData>();

  virtualHumans.forEach((item) => {
    if (item.名称?.trim()) {
      const name = item.名称.trim();

      // 如果指定了目标语言，优先匹配该语言的数字人
      if (targetLanguage && item.语种 === targetLanguage) {
        map.set(name, item);
      } else if (!map.has(name)) {
        // 如果没有指定语言或者还没有该名称的映射，则添加
        map.set(name, item);
      }
    }
  });

  return map;
}

// 辅助函数：从图片素材中获取URL
function getImageUrl(imageName: string, imageAssets: ImageAssetData[]): string {
  if (!imageName?.trim()) return '';

  const asset = imageAssets.find((item) => item.名称 === imageName.trim());
  if (!asset) return imageName; // 如果找不到，返回原始名称

  // 直接返回链接字段
  return asset.链接 || '';
}

function tryParseField(str: string): Record<string, unknown> {
  const entries = str.split('\n').map((line) =>
    line
      .trim()
      .split(/\s*[:：]\s*(.+)/)
      .slice(0, 2)
  );
  if (entries.length === 1 && entries[0].length === 1) {
    // 解析失败，只有 1 个字段
    throw new ParseError(`无法解析字段: ${str}`, str);
  }
  return Object.fromEntries(entries);
}

// 辅助函数：链接分镜数据到子表
function linkSceneDataToSubTables(
  rawSceneData: RawScriptSceneData[],
  imageAssets: ImageAssetData[],
  videoAssets: VideoAssetData[],
  events: EventData[],
  virtualHumans: VirtualHumanData[],
  scenes: SceneData[],
  targetLanguage?: string
): ScriptSceneData[] {
  // 创建查找映射表
  const eventMap = createNameMap(events);
  const virtualHumanMap = createVirtualHumanMap(virtualHumans, targetLanguage);
  const sceneMap = createNameMap(scenes);
  const videoMap = createNameMap(videoAssets);

  return rawSceneData.map((rawData) => {
    // 过滤掉重名检测相关数据
    const cleanData = { ...rawData };
    const fetchField = function <T>(
      name: keyof RawScriptSceneData,
      map: Map<string, T>
    ): T | null {
      return cleanData[name]
        ? map.get(cleanData[name]) ||
            (tryParseField(cleanData[name]) as T) ||
            null
        : null;
    };

    return {
      环节: cleanData.环节,
      台词: cleanData.台词,
      背景: getImageUrl(cleanData.背景, imageAssets),
      前景: getImageUrl(cleanData.前景, imageAssets),
      转场: getImageUrl(cleanData.转场, imageAssets),
      场景: fetchField('场景', sceneMap),
      数字人: fetchField('数字人', virtualHumanMap),
      视频: fetchField('视频', videoMap),
      音乐: fetchField('音乐', videoMap),
      事件: fetchField('事件', eventMap),
    };
  });
}

// 主解析函数
export function parseImportScript(xlsxBuffer: ArrayBuffer): ParsedScriptData {
  try {
    // 读取 ArrayBuffer 并解析为工作簿
    const workbook = XLSX.read(xlsxBuffer, { type: 'array' });

    const result: ParsedScriptDataWithRaw = {
      分镜数据: {},
      图片素材: [],
      音视频素材: [],
      事件配置: [],
      数字人配置: [],
      场景配置: [],
      检测到的语言: [],
    };

    // 用于收集所有检测到的语言代码
    const detectedLanguages = new Set<string>();

    // 遍历所有工作表
    workbook.SheetNames.forEach((sheetName) => {
      const worksheet = workbook.Sheets[sheetName];
      const jsonData = XLSX.utils.sheet_to_json(worksheet, {
        header: 1,
        defval: '',
      }) as (string | number)[][];

      if (jsonData.length === 0) return;

      // 根据工作表名称解析不同类型的数据
      if (sheetName === '教程' || sheetName.includes('教程')) {
        // 解析教程内容
        const tutorialContent = jsonData
          .flat()
          .filter((cell) => cell && String(cell).trim())
          .join('\n');
        result.教程 = tutorialContent;
      } else if (sheetName.includes('分镜') || sheetName.includes('语')) {
        // 先解析原始分镜数据
        const rawSceneData = parseTableData<RawScriptSceneData>(jsonData);
        if (rawSceneData.length > 0) {
          // 尝试提取语言代码，如果找到则使用ISO代码作为键，否则使用原始名称
          const languageCode = extractLanguageCode(sheetName);
          const key = languageCode || sheetName;

          // 暂时存储原始数据，稍后进行链接处理
          result[`_raw_${key}`] = rawSceneData;

          // 如果成功提取了语言代码，记录映射关系并添加到检测列表
          if (languageCode) {
            detectedLanguages.add(languageCode);
          }
        }
      } else if (sheetName === '图片' || sheetName.includes('图片')) {
        // 解析图片素材，合并语言URL为单一链接
        result.图片素材 = parseGenericTable<ImageAssetData>(jsonData, {
          mergeLanguageUrls: true,
          detectedLanguages,
          requiredField: '名称',
          validationRules: VALIDATION_RULES.图片素材,
          tableName: '图片素材',
        });
      } else if (
        sheetName === '音视频' ||
        sheetName.includes('音视频') ||
        sheetName.includes('视频')
      ) {
        // 解析音视频素材，合并语言URL为单一链接
        result.音视频素材 = parseGenericTable<VideoAssetData>(jsonData, {
          mergeLanguageUrls: true,
          detectedLanguages,
          requiredField: '名称',
          validationRules: VALIDATION_RULES.音视频素材,
          tableName: '音视频素材',
        });
      } else if (sheetName === '事件' || sheetName.includes('事件')) {
        // 解析事件配置
        result.事件配置 = parseGenericTable<EventData>(jsonData, {
          validationRules: VALIDATION_RULES.事件配置,
          tableName: '事件配置',
        });
      } else if (sheetName === '数字人' || sheetName.includes('数字人')) {
        // 解析数字人配置
        result.数字人配置 = parseGenericTable<VirtualHumanData>(jsonData, {
          validationRules: VALIDATION_RULES.数字人配置,
          tableName: '数字人配置',
        });
      } else if (sheetName === '场景' || sheetName.includes('场景')) {
        // 解析场景配置
        result.场景配置 = parseGenericTable<SceneData>(jsonData, {
          validationRules: VALIDATION_RULES.场景配置,
          tableName: '场景配置',
        });
      }
    });

    // 将检测到的语言添加到结果中
    result.检测到的语言 = Array.from(detectedLanguages).sort();

    // 处理分镜数据链接
    const resultWithRaw = result;
    Object.keys(resultWithRaw).forEach((key) => {
      if (key.startsWith('_raw_')) {
        const originalKey = key.replace('_raw_', '');
        const rawSceneData = resultWithRaw[key] as RawScriptSceneData[];

        // 提取语言代码用于数字人匹配
        const languageCode = extractLanguageCode(originalKey);

        // 链接分镜数据到子表
        const linkedSceneData = linkSceneDataToSubTables(
          rawSceneData,
          result.图片素材,
          result.音视频素材,
          result.事件配置,
          result.数字人配置,
          result.场景配置,
          languageCode || undefined
        );

        result.分镜数据[originalKey] = linkedSceneData;

        // 删除临时的原始数据
        delete resultWithRaw[key];
      }
    });

    console.log('解析结果:', result);
    playService.getPageSchema(result).then((schema) => {
      console.log('schema', schema);
    });
    return result;
  } catch (error) {
    console.error('Excel解析失败:', error);
    throw new Error(
      `Excel解析失败: ${error instanceof Error ? error.message : '未知错误'}`
    );
  }
}
