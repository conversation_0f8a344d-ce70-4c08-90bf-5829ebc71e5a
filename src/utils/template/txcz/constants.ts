import { getParam } from '@tencent/midas-util';
import { VIDEO_POSITION_COMMON_STYLE_KEY, VIRTUALMAN_POSE } from './type';

export const CURRENT_LANGUAGE = getParam('language') || 'en';

// 直播拉流标签
export const LIVE_PLAY_PULL_KEYWORD = '主播打游戏';

function randomChoose<T>(arg: T[]): T {
  return arg[Math.floor(Math.random() * arg.length)];
}

// https://doc.weixin.qq.com/sheet/e3_AFgAzwa0ACktmjQz2g1S1KMtlDPYK?scode=AJEAIQdfAAoqX08ve8AFgAzwa0ACk

// 1127西西_站姿_圣诞节
// 1920x1080	246e6e7ccb5d4d2d91e7563e6861380d
// 1080x1920	d4d53abfe83841b6a2b7c73025300fe2

// 1120西西_站姿_黑T
// 1920x1080	22d45320ff8b467795d9d348f139d176
// 1080x1920	0d6ed19f14de46caa62d4db918747c70

// 1127西西_手机支架_圣诞节
// 1920x1080	374ca31cba064cf1929116586dc68b41
// 1080x1920	c88b968de56d49dfb9c464ce3b136d44

// 1127西西_坐姿_圣诞节
// 1920x1080	6a1b79b1b14d401583b0de1f435b8dc2
// 1080x1920	25403d8914f040d18fab03e577da82c4

// 西西_坐姿_横屏手机
// 1080x1920 b8eecbb7ff9644c28b3cce098847aba5

// 西西_坐姿_竖屏手机
// 1080x1920 b3524c25c43940e6ab448dd542400ee4

// 默德-橘色毛衣-坐姿
// 1080x1920	769d7bda153cf58725ee17073239e615
// 1920x1080	f992be51beb28c2a8eb3bb1928a86d37

// 默德-橘色毛衣-坐姿打游戏
// 1920x1080	41017f860baf7029afcfbbc01965bcd3
// 1080x1920	41397f27c22a3c28898829e5a553f005

// 默德-橘色毛衣-坐姿手机支架
// 1920x1080	9e874c82ea4419519e61026dcd2f5b79
// 1080x1920	1f53b22d587672bf30aad6638136c937

const 西西坐姿 = {
  key: '619f7b8c75f84470920f34a058a04e45',
  label: '默认',
  config: null,
};
const 自研西西坐姿 = {
  key: '4c0ec0ee8ed89db991156c51504b368d',
  config: {
    key: '4c0ec0ee8ed89db991156c51504b368d',
    type: 'pagedoo',
    enabled: true,
    img: '',
    label: '',
    desc: '',
    chromaKey: 0.5,
    timbre: {
      voice_id: 'MiXi2',
      emotion: 'default',
      voice_speed: 1,
      character_id: 'MiXi2',
    },
  },
  label: '默认',
};
const 西西竖屏手机 = {
  key: '011e24ae22ec4673adc64f085dbf1d77',
  label: '手持手机-竖屏',
  config: null,
};
const 自研西西竖屏手机 = {
  key: '476e9156a6cb3c598963faffeb6052f9',
  config: {
    key: '476e9156a6cb3c598963faffeb6052f9',
    type: 'pagedoo',
    enabled: true,
    img: '',
    label: '',
    desc: '',
    chromaKey: 0.5,
    timbre: {
      voice_id: 'MiXi2',
      emotion: 'default',
      voice_speed: 1,
      character_id: 'MiXi2',
    },
  },
  label: '手持手机-竖屏',
};
const 中东哥竖屏手机 = {
  key: '1f53b22d587672bf30aad6638136c937',
  label: '手持手机-竖屏',
  config: null,
};
const 自研中东哥竖屏手机 = {
  key: '4733c1803a3144efabb899ce49f21fcc',
  config: {
    key: '4733c1803a3144efabb899ce49f21fcc',
    type: 'pagedoo',
    enabled: true,
    img: '',
    label: '',
    desc: '',
    chromaKey: 0.5,
    timbre: {
      voice_id: 'MiXi2',
      emotion: 'default',
      voice_speed: 1,
      character_id: 'MiXi2',
    },
  },
  label: '手持手机-竖屏',
};

// 数字人id
export const VIRTUALMAN: Record<
  VIRTUALMAN_POSE,
  { key: string; label: string; config: any }
> = {
  [VIRTUALMAN_POSE.human_primary]:
    {
      en: 自研西西坐姿,
    }[CURRENT_LANGUAGE] || 西西坐姿,
  [VIRTUALMAN_POSE.using_phone_in_portrait_orientation]:
    {
      en: 自研中东哥竖屏手机,
    }[CURRENT_LANGUAGE] || 中东哥竖屏手机,
  [VIRTUALMAN_POSE.using_phone_in_landscape_orientation]:
    {
      en: 西西竖屏手机,
    }[CURRENT_LANGUAGE] || 西西竖屏手机,
};
export const VIDEO_ORIENTATION = {
  [VIRTUALMAN_POSE.human_primary]: VIDEO_POSITION_COMMON_STYLE_KEY.portrait,
  // VIDEO_POSITION_COMMON_STYLE_KEY.fullscreen,
  [VIRTUALMAN_POSE.using_phone_in_portrait_orientation]:
    VIDEO_POSITION_COMMON_STYLE_KEY.portrait,
  [VIRTUALMAN_POSE.using_phone_in_landscape_orientation]:
    VIDEO_POSITION_COMMON_STYLE_KEY.landscape,
};

// 展示全屏视频标识
export const FULL_VIDEO_KEYWORD = '全屏视频';

export const SUB_VIRTUALMAN_KEYWORD = '【陪伴模式】';

// 数字人置于裁剪形状区域标识
export const CLIPPING_SHAPE_KEYWORD = '【小人版】';

// 互动标识
export const COMMENT_MODE_KEYWORD = '互动';

// 是否使用腾讯云TTS
export const useTencentCloudTTS = false;

// EXCEL行起始标识
export const excelLineStartFlag = '%%%%%%';
// EXCEL行结束标识
export const excelLineEndFlag = '@@@@@@';

// 视频场景（含游戏直接）的背景图片
export const VIDEO_BKG_URL = {
  [VIDEO_POSITION_COMMON_STYLE_KEY.landscape]:
    'https://pagedoo.pay.qq.com/material/@platform/737131b53ca353a70affaadc32669b1b.png',
  [VIDEO_POSITION_COMMON_STYLE_KEY.portrait]: '',
  [VIDEO_POSITION_COMMON_STYLE_KEY.fullscreen]: '',
};

export const miTaemotion = {
  默认: 'default',
  互动引导: 'interactive',
  下播: 'goodbye',
  转折: 'turning',
  打招呼: 'say_hi',
  打游戏ing: 'game_playing',
  赢了游戏: 'win_game',
  输了游戏: 'lost_game',
};
export const miTaEmotionMap = {
  default: 'b8cdaebf-c6df-41e2-b164-1bac016b4b0d',
  interactive: '4406865b-924c-4dac-9903-2b784ff454a8',
  goodbye: '71c1b0c5-17c6-4edb-961d-19caa2914182',
  turning: '4c407bc0-157d-4a34-9aa2-65c5698e4a0d',
  say_hi: '53d3ac2b-baab-45e1-a790-9366ca6910ea',
  game_playing: 'b3f51cde-2e5b-484e-9f72-1e0518821210',
  win_game: '7a886c3f-ce4a-468a-b01d-f2d291eccfb6',
  lost_game: '13c8d804-3a03-4ee5-b010-ff763fb5ab46',
};
export const miXiemotion = {
  默认: 'default',
  互动引导: 'interactive',
  下播: 'goodbye',
  转折: 'turning',
  打招呼: 'say_hi',
  打游戏ing: 'game_playing',
  赢了游戏: 'win_game',
  输了游戏: 'lost_game',
};
export const miXiEmotionMap = {
  default: '1874f515-d9af-4b75-9625-caa9c7823893',
  interactive: '11977bdc-580a-42ab-a8d4-68204bfe6c54',
  goodbye: 'bfd84d4e-7d28-4efd-94c1-4838b6e755a0',
  turning: '4eacac72-ac9a-49cd-9800-c1f3e3d420a4',
  say_hi: '41be7cec-e71b-49a2-ad39-9bdc17a06df6',
  game_playing: '0c350cf6-634b-4f6b-9a11-5bc4f9cbad4a',
  win_game: 'a62cd8a4-8dbd-4a37-8a7f-4e8ba3f73767',
  lost_game: '793e3b84-d100-47b6-ad63-661a510ac81c',
};

export const CSV_PREFIX = `环节,"素材名称
命名规范：[类型]_[环节]_[出现顺序].[格式]
格式：图片为png，视频为mp4",备注,已提供,,,,,,,,,,,,,,,,,,,,,,,`;

export const VIRTUALMAN_ID_MAP = {};

export const VOICE_ID_MAP: Record<
  string,
  {
    id: string;
    gender?: string;
    type?: string;
    pagedoo?: any;
    style?: string;
    speed?: number;
    locale: string;
  }
> = {
  en: {
    id: 'en-US-JaneNeural',
    gender: 'Female',
    style: 'cheerful',
    locale: 'en-US',
  },
  en2: {
    id: 'en-US-TonyNeural',
    gender: 'Male',
    style: 'cheerful',
    locale: 'en-US',
  },
  ar: {
    id: 'en-US-AvaMultilingualNeural',
    gender: 'Female',
    locale: 'ar-AE',
  },
  ar2: {
    id: 'en-US-AndrewMultilingualNeural',
    locale: 'ar-AE',
    gender: 'Male',
  },
  de: {
    id: 'en-US-AvaMultilingualNeural',
    gender: 'Female',
    locale: 'de-DE',
  },
  tr: {
    id: 'en-US-AvaMultilingualNeural',
    gender: 'Female',
    locale: 'tr-TR',
  },
  tr2: {
    id: 'en-US-AndrewMultilingualNeural',
    locale: 'tr-TR',
    gender: 'Male',
  },
  ru: {
    id: 'en-US-AvaMultilingualNeural',
    gender: 'Female',
    locale: 'ru-RU',
  },
  id: {
    id: 'en-US-AvaMultilingualNeural',
    gender: 'Female',
    locale: 'id-ID',
  },
  ms: {
    id: 'en-US-AvaMultilingualNeural',
    gender: 'Female',
    locale: 'ms-MY',
  },
  th: {
    id: 'en-US-AvaMultilingualNeural',
    gender: 'Female',
    locale: 'th-TH',
  },
  // hk: {
  //   id: 'zh-HK-HiuMaanNeural',
  //   speed: 1.1,
  //   locale: 'zh-HK',
  // },
  // tw: {
  //   id: 'zh-CN-XiaozhenNeural',
  //   speed: 1.1,
  //   locale: 'zh-CN',
  // },
  zh: {
    id: 'zh-CN-XiaozhenNeural',
    gender: 'Female',
    speed: 1.1,
    locale: 'zh-CN',
  },
};

export const LANGUAGE_SPEED = {
  ar: 233,
  de: 233,
  en: 233,
  id: 233,
  ms: 233,
  ru: 233,
  th: 233,
  tr: 233,
  zh: 88,
}[CURRENT_LANGUAGE];

export const SCRIPT = {
  ar: '',
  de: '',
  en: '',
  id: '',
  ms: '',
  ru: '',
  th: '',
  tr: '',
  zh: '',
}[CURRENT_LANGUAGE] as string;
